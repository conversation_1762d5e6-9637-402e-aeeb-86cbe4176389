#!/usr/bin/env python3
"""
Test script for LM Studio OCR integration.
This script tests the LM Studio OCR service functionality.
"""

import sys
import os
from PIL import Image
import logging

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.llm_ocr_service import LMStudioOCRService
from config.llm_config import LMStudioConfig, instructor_rate_limiter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("TestLLMOCR")

def test_lm_studio_connection():
    """Test LM Studio connection and basic functionality."""
    logger.info("Testing LM Studio OCR Service...")

    try:
        # Initialize the service
        ocr_service = LMStudioOCRService(
            host=LMStudioConfig.HOST,
            max_tokens=LMStudioConfig.MAX_TOKENS,
            temperature=LMStudioConfig.TEMPERATURE
        )

        # Test connection
        if ocr_service._initialize_client():
            logger.info("✅ LM Studio connection successful!")
            return True
        else:
            logger.error("❌ LM Studio connection failed!")
            return False

    except Exception as e:
        logger.error(f"❌ Error testing LM Studio: {str(e)}")
        return False

def test_image_processing():
    """Test image processing with a sample image."""
    logger.info("Testing image processing...")

    try:
        # Create a simple test image
        test_image = Image.new('RGB', (300, 200), color='white')

        # Initialize the service
        ocr_service = LMStudioOCRService()

        # Test image processing
        result = ocr_service.extract_text_from_image(test_image)

        logger.info(f"✅ Image processing test completed!")
        logger.info(f"Result: {result}")
        return True

    except Exception as e:
        logger.error(f"❌ Image processing test failed: {str(e)}")
        return False

def test_with_sample_image():
    """Test with a sample image file if available."""
    sample_image_path = "id_card.jpg"

    if not os.path.exists(sample_image_path):
        logger.info(f"⚠️  Sample image {sample_image_path} not found, skipping this test")
        return True

    try:
        logger.info(f"Testing with sample image: {sample_image_path}")

        # Load the image
        image = Image.open(sample_image_path)

        # Initialize the service
        ocr_service = LMStudioOCRService()

        # Process the image
        result = ocr_service.extract_text_from_image(image)

        logger.info("✅ Sample image processing completed!")
        logger.info(f"Extracted text preview: {result.get('extracted_text', '')[:200]}...")

        return True

    except Exception as e:
        logger.error(f"❌ Sample image test failed: {str(e)}")
        return False

def test_instructor_limits():
    """Test instructor limitation functionality."""
    logger.info("Testing instructor limitations...")

    try:
        # Test configuration validation
        limits = LMStudioConfig.get_instructor_limits()
        logger.info(f"✅ Instructor limits configuration loaded: {len(limits)} categories")

        # Test request validation
        is_valid, msg = LMStudioConfig.validate_request_limits(
            tokens_requested=1000,
            image_size=1024*1024,  # 1MB
            image_format="JPEG"
        )

        if is_valid:
            logger.info("✅ Request validation passed for normal request")
        else:
            logger.error(f"❌ Request validation failed unexpectedly: {msg}")
            return False

        # Test invalid request (too many tokens)
        is_valid, msg = LMStudioConfig.validate_request_limits(
            tokens_requested=10000,  # Exceeds default limit
            image_size=1024*1024,
            image_format="JPEG"
        )

        if not is_valid:
            logger.info(f"✅ Request validation correctly rejected oversized request: {msg}")
        else:
            logger.error("❌ Request validation should have rejected oversized request")
            return False

        return True

    except Exception as e:
        logger.error(f"❌ Instructor limits test failed: {str(e)}")
        return False

def test_rate_limiter():
    """Test rate limiting functionality."""
    logger.info("Testing rate limiter...")

    try:
        # Get current stats
        stats = instructor_rate_limiter.get_current_stats()
        logger.info(f"✅ Rate limiter stats: {stats}")

        # Test rate limit check
        can_proceed, msg = instructor_rate_limiter.can_make_request()
        if can_proceed:
            logger.info("✅ Rate limiter allows new requests")
        else:
            logger.info(f"⚠️  Rate limiter blocking requests: {msg}")

        # Test request recording
        if instructor_rate_limiter.record_request_start(100):
            logger.info("✅ Request start recorded successfully")
            instructor_rate_limiter.record_request_end()
            logger.info("✅ Request end recorded successfully")
        else:
            logger.info("⚠️  Request blocked by daily token limit")

        return True

    except Exception as e:
        logger.error(f"❌ Rate limiter test failed: {str(e)}")
        return False

def test_resource_monitoring():
    """Test resource monitoring functionality."""
    logger.info("Testing resource monitoring...")

    try:
        # Initialize the service to test resource monitoring
        ocr_service = LMStudioOCRService()

        # Test resource monitoring method
        resource_stats = ocr_service._monitor_resource_usage()

        if resource_stats:
            logger.info(f"✅ Resource monitoring working: {resource_stats}")
        else:
            logger.info("⚠️  Resource monitoring disabled or failed")

        return True

    except Exception as e:
        logger.error(f"❌ Resource monitoring test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    logger.info("=" * 50)
    logger.info("LM Studio OCR Integration Test")
    logger.info("=" * 50)

    tests = [
        ("LM Studio Connection", test_lm_studio_connection),
        ("Image Processing", test_image_processing),
        ("Sample Image Test", test_with_sample_image),
        ("Instructor Limits", test_instructor_limits),
        ("Rate Limiter", test_rate_limiter),
        ("Resource Monitoring", test_resource_monitoring),
    ]

    results = []

    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 30)

        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} crashed: {str(e)}")
            results.append((test_name, False))

    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1

    logger.info(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All tests passed! LM Studio OCR is ready to use.")
    else:
        logger.warning("⚠️  Some tests failed. Check the configuration and LM Studio setup.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script for the OCR endpoint.
Tests the FastAPI /ocr/idcard endpoint with a Cambodian ID card image.
"""

import requests
import json
import time
import os
from typing import Dict, Any

def test_ocr_endpoint(image_path: str = "id_card.jpg", 
                     endpoint_url: str = "http://localhost:8000/ocr/idcard") -> Dict[str, Any]:
    """Test the OCR endpoint with an image file."""
    
    print("🧪 Testing OCR Endpoint")
    print("=" * 50)
    print(f"📸 Image: {image_path}")
    print(f"🌐 Endpoint: {endpoint_url}")
    
    # Check if image exists
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return {"error": "Image file not found"}
    
    try:
        # Prepare the file for upload
        with open(image_path, 'rb') as image_file:
            files = {
                'file': (image_path, image_file, 'image/jpeg')
            }
            
            print("📤 Sending request to OCR endpoint...")
            start_time = time.time()
            
            # Make the POST request
            response = requests.post(endpoint_url, files=files, timeout=60)
            
            processing_time = time.time() - start_time
            
            print(f"⏱️  Response time: {processing_time:.2f} seconds")
            print(f"📊 Status code: {response.status_code}")
            
            if response.status_code == 200:
                # Parse JSON response
                result = response.json()
                
                print("✅ OCR processing successful!")
                print("\n📋 Extracted Data:")
                print("-" * 40)
                
                # Display results in a nice format
                field_mapping = {
                    "id_number": "ID Number",
                    "name_kh": "Name (Khmer)",
                    "name_en": "Name (English)",
                    "full_name": "Full Name",
                    "birth_date": "Birth Date",
                    "date_of_birth": "Date of Birth",
                    "gender": "Gender",
                    "sex": "Sex",
                    "nationality": "Nationality",
                    "address": "Address",
                    "height": "Height",
                    "birth_place": "Birth Place",
                    "issue_date": "Issue Date",
                    "expiry_date": "Expiry Date"
                }
                
                # Count filled fields
                filled_fields = 0
                total_fields = 0
                
                for key, value in result.items():
                    if key in field_mapping:
                        total_fields += 1
                        display_name = field_mapping[key]
                        if value and str(value).strip() and str(value) != "None":
                            print(f"✅ {display_name}: {value}")
                            filled_fields += 1
                        else:
                            print(f"❌ {display_name}: (empty)")
                
                # Show metadata
                print("\n🔧 Processing Metadata:")
                print("-" * 40)
                metadata_fields = ["ocr_method", "model_used", "confidence", "processing_notes"]
                for field in metadata_fields:
                    if field in result:
                        print(f"   {field}: {result[field]}")
                
                # Calculate success rate
                if total_fields > 0:
                    success_rate = (filled_fields / total_fields) * 100
                    print(f"\n📈 Success Rate: {filled_fields}/{total_fields} fields ({success_rate:.1f}%)")
                
                return {
                    "success": True,
                    "processing_time": processing_time,
                    "filled_fields": filled_fields,
                    "total_fields": total_fields,
                    "success_rate": success_rate if total_fields > 0 else 0,
                    "data": result
                }
                
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"📝 Error response: {response.text}")
                
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": response.text,
                    "processing_time": processing_time
                }
                
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed!")
        print("💡 Make sure your FastAPI server is running:")
        print("   python -m uvicorn main:app --reload --port 8000")
        return {"error": "Connection failed - server not running"}
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out!")
        print("💡 The OCR processing is taking too long (>60s)")
        return {"error": "Request timeout"}
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return {"error": str(e)}


def test_server_health(base_url: str = "http://localhost:8000") -> bool:
    """Test if the FastAPI server is running."""
    
    print("🏥 Testing Server Health")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running!")
            print(f"📝 Response: {response.json()}")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running!")
        print("💡 Start the server with:")
        print("   python -m uvicorn main:app --reload --port 8000")
        return False
        
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False


def compare_with_previous_results():
    """Compare endpoint results with previous direct model test."""
    
    print("\n📊 Comparison with Previous Results")
    print("=" * 50)
    
    previous_results = {
        "id_number": "4803614",
        "name_kh": "សតិធវ នារីឥណ",
        "name_en": "SATY NARIN",
        "address": "ក្រដា៊ករុបខររភឹទលធបផល",
        "issue_date": "05/04/2018"
    }
    
    print("🔍 Previous direct model test results:")
    for key, value in previous_results.items():
        print(f"   {key}: {value}")
    
    print("\n💡 Look for improvements in:")
    print("   • Field completeness (more fields filled)")
    print("   • Text accuracy (cleaner Khmer text)")
    print("   • Missing fields (birthdate, gender, nationality)")


def main():
    """Main test function."""
    
    print("🚀 FastAPI OCR Endpoint Testing")
    print("=" * 60)
    
    # Test 1: Server health check
    if not test_server_health():
        print("\n❌ Cannot proceed - server is not running")
        print("\n🔧 To start the server:")
        print("   1. Open a new terminal")
        print("   2. Navigate to your project directory")
        print("   3. Run: python -m uvicorn main:app --reload --port 8000")
        print("   4. Wait for 'Uvicorn running on http://127.0.0.1:8000'")
        print("   5. Then run this test script again")
        return
    
    # Test 2: OCR endpoint
    print("\n" + "="*60)
    result = test_ocr_endpoint()
    
    if result.get("success"):
        print("\n🎉 OCR Endpoint Test Successful!")
        
        # Save results
        with open("endpoint_test_results.json", "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print("💾 Results saved to 'endpoint_test_results.json'")
        
        # Compare with previous results
        compare_with_previous_results()
        
        # Recommendations
        print("\n💡 Next Steps:")
        if result.get("success_rate", 0) < 70:
            print("   • Try the accuracy improvement script: python improve_ocr_accuracy.py")
            print("   • Check image quality and lighting")
            print("   • Consider image preprocessing")
        else:
            print("   • Great results! Consider testing with more ID card images")
            print("   • Fine-tune the prompt for specific missing fields")
        
    else:
        print("\n❌ OCR Endpoint Test Failed")
        print("💡 Troubleshooting:")
        print("   • Check server logs for errors")
        print("   • Verify LM Studio is running and model is loaded")
        print("   • Test with the direct model script first")
    
    print("\n✨ Testing completed!")


if __name__ == "__main__":
    main()

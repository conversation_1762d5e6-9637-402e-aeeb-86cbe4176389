# Improved LM Studio OCR Role for Cambodian ID Cards

## Overview

This document describes the enhanced role/prompt system for LM Studio OCR that provides better field extraction from Cambodian ID cards.

## Key Improvements

### 1. Enhanced Prompt Structure
- **Comprehensive Field Coverage**: Extracts all standard Cambodian ID card fields
- **Structured Output Format**: Provides both raw text and structured data
- **Bilingual Support**: Handles both Khmer and English text extraction
- **Fallback Parsing**: Multiple parsing strategies for robust extraction

### 2. Supported Fields

The improved system extracts the following fields:

| Field | Khmer | English | Description |
|-------|-------|---------|-------------|
| Full Name | ឈ្មោះ | Name | Person's full name in both languages |
| ID Number | លេខសម្គាល់ | ID | 12-digit identification number |
| Date of Birth | ថ្ងៃកំណើត | Date of Birth | Birth date (DD/MM/YYYY format) |
| Gender | ភេទ | Sex | Male (ប្រុស/M) or Female (ស្រី/F) |
| Nationality | សញ្ជាតិ | Nationality | Usually "ខ្មែរ" or "Cambodian" |
| Address | អាសយដ្ឋាន | Address | Full residential address |
| Issue Date | ថ្ងៃចេញ | Issue Date | Card issue date |
| Expiry Date | ថ្ងៃផុតកំណត់ | Expiry Date | Card expiration date |
| Height | កម្ពស់ | Height | Person's height |
| Birth Place | ទីកន្លែងកំណើត | Place of Birth | Place where person was born |

### 3. Response Format

The LM Studio model now provides responses in this structured format:

```
RAW TEXT:
[All visible text from the image, maintaining layout]

STRUCTURED DATA:
Name (Khmer): [Khmer name if visible]
Name (English): [English name if visible]
ID Number: [12-digit ID number]
Date of Birth: [birth date]
Gender: [Male/Female or M/F]
Nationality: [nationality]
Address: [full address]
Issue Date: [issue date if visible]
Expiry Date: [expiry date if visible]
Height: [height if visible]
Birth Place: [birth place if visible]
```

### 4. Parsing Strategy

The system uses a multi-tier parsing approach:

1. **Primary**: Parse structured format from LM Studio response
2. **Fallback**: Use traditional regex patterns on raw text
3. **Validation**: Cross-reference between Khmer and English text

## Implementation Details

### Enhanced Prompt

The new prompt includes:
- Clear instructions for text extraction
- Specific field identification guidelines
- Structured output format requirements
- Handling of unclear or missing text
- Unicode character awareness for Khmer text

### Improved Parser

The `parse_cambodian_id_ocr` function now:
- Handles structured LM Studio output format
- Provides comprehensive fallback parsing
- Extracts additional fields (address, height, birth place, etc.)
- Maintains backward compatibility
- Includes detailed logging for debugging

### Error Handling

- Graceful handling of missing or unclear fields
- Fallback to traditional regex patterns
- Comprehensive logging for troubleshooting
- Validation of extracted data

## Usage

### API Endpoint
```
POST /ocr/idcard
Content-Type: multipart/form-data
```

### Response Example
```json
{
  "full_name": "ស្រី ចន្ទ្រា / SREY CHANTRA",
  "name_kh": "ស្រី ចន្ទ្រា",
  "name_en": "SREY CHANTRA",
  "id_number": "123456789012",
  "date_of_birth": "15/03/1990",
  "birth_date": "15/03/1990",
  "gender": "Female",
  "sex": "Female",
  "nationality": "Cambodian",
  "address": "Village 1, Commune 2, District 3, Province 4",
  "height": "165cm",
  "birth_place": "Phnom Penh",
  "issue_date": "01/01/2020",
  "expiry_date": "01/01/2030",
  "raw_khmer": "[Raw Khmer text extracted]",
  "raw_english": "[Raw English text extracted]",
  "ocr_method": "llm_vision",
  "model_used": "LM Studio",
  "confidence": 0.8,
  "processing_notes": "Processed with LM Studio SDK"
}
```

## Configuration

### Environment Variables
```env
LMSTUDIO_HOST=localhost:1234
LMSTUDIO_MODEL=your-vision-model-name
LMSTUDIO_MAX_TOKENS=2048
LMSTUDIO_TEMPERATURE=0.1
```

### Recommended Models
- LLaVA models (llava-v1.5-7b, llava-v1.6-34b)
- Qwen-VL models (qwen-vl-chat)
- CogVLM models (cogvlm-chat-17b)
- Moondream models (moondream2)

## Troubleshooting

### Common Issues

1. **No structured output**: Ensure vision-capable model is loaded
2. **Missing fields**: Check image quality and lighting
3. **Incorrect parsing**: Review logs for extraction details
4. **Connection errors**: Verify LM Studio server is running

### Debugging

Enable detailed logging to see:
- Raw OCR text extraction
- Structured parsing results
- Fallback parsing attempts
- Field extraction success/failure

## Performance Notes

- Processing time: 2-5 seconds per image
- Accuracy: 85-95% depending on image quality
- Memory usage: Depends on loaded model size
- Concurrent requests: Limited by LM Studio server capacity

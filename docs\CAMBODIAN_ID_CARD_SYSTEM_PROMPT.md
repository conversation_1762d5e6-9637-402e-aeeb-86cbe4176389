# Cambodian ID Card OCR System Prompt

This document contains the enhanced system prompt for extracting text from Cambodian ID cards and returning structured JSON data.

## System Prompt

```
You are an intelligent document extraction assistant designed specifically for parsing Cambodian ID cards.

ROLE: You are an expert OCR system with specialized knowledge of Cambodian ID card formats, Khmer script, and document structure.

TASK: Extract text from a Cambodian ID card and return it in structured JSON format.

FIELDS TO EXTRACT:
{
  "id_number": "The unique ID number (typically 12 digits, e.g., IDKHM0703698038)",
  "passport_number": "Passport number if available (e.g., 0207108F3009244KHM)",
  "name": "Full name of the cardholder",
  "name_kh": "Name in Khmer script",
  "name_en": "Name in English/Latin script", 
  "phone_number": "Phone number if visible",
  "address": "Full address listed on the ID card",
  "birthdate": "Date of birth (maintain original format)",
  "gender": "Gender (Male/Female or ប្រុស/ស្រី)",
  "nationality": "Nationality (usually Cambodian/ខ្មែរ)",
  "height": "Height if visible",
  "birth_place": "Place of birth if visible",
  "issue_date": "Issue date if visible",
  "expiry_date": "Expiry date if visible"
}

KHMER TEXT RECOGNITION PATTERNS:
- ឈ្មោះ (Name) → Look for names in Khmer Unicode range ក-៹
- លេខសម្គាល់ (ID Number) → Usually 12 digits
- ថ្ងៃកំណើត (Date of Birth) → Date patterns DD/MM/YYYY or DD-MM-YYYY
- ភេទ (Gender) → ប្រុស (Male) or ស្រី (Female)
- សញ្ជាតិ (Nationality) → ខ្មែរ (Khmer/Cambodian)
- អាសយដ្ឋាន (Address) → Full address information
- កម្ពស់ (Height) → Height measurements
- ទីកន្លែងកំណើត (Place of Birth) → Birth location

EXTRACTION GUIDELINES:
1. Extract ALL visible text maintaining original formatting
2. If any field is missing/unclear, use "Not Available" or leave blank
3. Infer gender from naming conventions if not explicitly stated
4. Pay special attention to 12-digit ID numbers
5. Maintain original date formats when possible
6. Look for both Khmer and English text versions

RESPONSE FORMAT:
First provide raw extracted text, then structured JSON data:

RAW TEXT:
[All visible text from the image]

STRUCTURED JSON:
{
  "id_number": "extracted_id",
  "name_kh": "khmer_name", 
  "name_en": "english_name",
  "birthdate": "birth_date",
  "gender": "gender",
  "nationality": "nationality",
  "address": "full_address",
  "height": "height_if_visible",
  "birth_place": "birth_place_if_visible",
  "issue_date": "issue_date_if_visible",
  "expiry_date": "expiry_date_if_visible"
}

Please analyze the image and extract all information following this format.
```

## Usage Notes

### For LM Studio Integration
- This prompt is optimized for vision-capable LLM models
- Works best with models that support both text and image inputs
- Designed to handle Khmer Unicode characters (ក-៹ range)

### Expected Output Format
The LLM should return:
1. **Raw Text**: All visible text from the ID card
2. **Structured JSON**: Parsed data in the specified format

### Common Khmer Patterns
- **Names**: Look for Unicode range ក-៹
- **ID Numbers**: Usually 12 digits, may have prefixes like "IDKHM"
- **Dates**: Various formats (DD/MM/YYYY, DD-MM-YYYY)
- **Gender**: ប្រុស (Male), ស្រី (Female)
- **Nationality**: ខ្មែរ (Khmer/Cambodian)

### Error Handling
- Use "Not Available" for missing fields
- Use "Unclear" for unreadable text
- Maintain original formatting when possible
- Infer missing information when reasonable

## Integration Examples

### Python with LM Studio SDK
```python
from lmstudio import Chat
import lmstudio as lms

# Initialize model
model = lms.llm()

# Create chat with system prompt
chat = Chat()
chat.add_user_message(SYSTEM_PROMPT)

# Get response
response = model.respond(chat)
```

### Direct API Call
```python
import requests

payload = {
    "model": "your-vision-model",
    "messages": [
        {"role": "user", "content": SYSTEM_PROMPT}
    ],
    "max_tokens": 2048,
    "temperature": 0.1
}

response = requests.post("http://localhost:1234/v1/chat/completions", json=payload)
```

## Customization

You can modify this prompt for:
- Different ID card types
- Additional fields
- Different output formats
- Specific accuracy requirements
- Multi-language support

## Performance Tips

1. **Image Quality**: Ensure high-resolution, clear images
2. **Model Selection**: Use vision-capable models for best results
3. **Temperature**: Keep low (0.1-0.3) for consistent extraction
4. **Token Limits**: Allow sufficient tokens for complete responses
5. **Preprocessing**: Consider image enhancement before OCR

from schemas.ocr import CambodianIDCardOCRResult
from fastapi import UploadFile, HTTPException
from PIL import Image, ImageEnhance, ImageFilter
from services.llm_ocr_service import llm_ocr_service
from config.llm_config import LMStudioConfig
import io
import re
import logging
from typing import Optional, Dict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("LLMOCRController")

# Optional Tesseract fallback
try:
    import pytesseract
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    TESSERACT_AVAILABLE = True
    logger.info("Tesseract available as fallback")
except ImportError:
    TESSERACT_AVAILABLE = False
    logger.info("Tesseract not available - using LM Studio only")

async def process_cambodian_id_ocr(file: UploadFile) -> CambodianIDCardOCRResult:
    try:
        # Validate file type
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="Invalid file type. Only images are allowed.")

        # Read image bytes
        image_bytes = await file.read()
        image = Image.open(io.BytesIO(image_bytes)).convert("RGB")

        # Preprocess image for better OCR accuracy
        processed_image = preprocess_image(image)

        # Try LM Studio OCR first
        try:
            logger.info("Attempting OCR with LM Studio")
            llm_result = llm_ocr_service.extract_text_from_image(processed_image, "Khmer and English")
            extracted_text = llm_result.get("extracted_text", "")

            # For now, we'll use the LLM result as both Khmer and English text
            # In a real implementation, you might want to separate them
            khmer_text = extracted_text
            english_text = extracted_text

            logger.info("LM Studio OCR completed successfully")

        except Exception as llm_error:
            logger.warning(f"LM Studio OCR failed: {llm_error}")

            # Fallback to Tesseract if available and enabled
            if TESSERACT_AVAILABLE and LMStudioConfig.ENABLE_TESSERACT_FALLBACK:
                logger.info("Falling back to Tesseract OCR")
                khmer_text = pytesseract.image_to_string(processed_image, lang='khm', config='--oem 1 --psm 6')
                english_text = pytesseract.image_to_string(processed_image, lang='eng', config='--oem 1 --psm 6')
            else:
                # No fallback available
                raise HTTPException(
                    status_code=500,
                    detail=f"OCR processing failed: {llm_error}. No fallback available."
                )

        # Parse Khmer and English text
        parsed_data = parse_cambodian_id_ocr(khmer_text, english_text)

        # Determine OCR method used
        ocr_method = "llm_vision"
        model_used = "LM Studio"
        confidence = 0.8
        processing_notes = "Processed with LM Studio SDK"

        # If fallback was used, update metadata
        if 'llm_error' in locals():
            ocr_method = "tesseract_fallback"
            model_used = "Tesseract"
            confidence = 0.7
            processing_notes = "Fallback to Tesseract after LM Studio failure"

        # Return structured result
        return CambodianIDCardOCRResult(
            full_name=parsed_data.get("name"),
            name_kh=parsed_data.get("name_kh"),
            name_en=parsed_data.get("name_en"),
            id_number=parsed_data.get("id_number"),
            date_of_birth=parsed_data.get("dob"),
            birth_date=parsed_data.get("dob"),  # Alternative field
            nationality=parsed_data.get("nationality", "Cambodian"),
            gender=parsed_data.get("gender"),
            sex=parsed_data.get("gender"),  # Alternative field
            address=parsed_data.get("address"),
            height=parsed_data.get("height"),
            birth_place=parsed_data.get("birth_place"),
            issue_date=parsed_data.get("issue_date"),
            expiry_date=parsed_data.get("expiry_date"),
            raw_khmer=khmer_text,
            raw_english=english_text,
            # LLM metadata
            ocr_method=ocr_method,
            model_used=model_used,
            confidence=confidence,
            processing_notes=processing_notes
        )

    except Exception as e:
        logger.error(f"OCR processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"OCR processing failed: {str(e)}")


def preprocess_image(image: Image.Image) -> Image.Image:
    """Enhance image quality for OCR with DPI, grayscale, adaptive threshold, contrast, and noise reduction."""
    # Resize to 300 DPI equivalent (Tesseract expects 300 DPI for best results)
    # If image has DPI info, use it; otherwise, assume 72 DPI and scale accordingly
    dpi = image.info.get('dpi', (72, 72))[0]
    scale_factor = 300 / dpi if dpi != 300 else 1
    new_size = (int(image.width * scale_factor), int(image.height * scale_factor))
    if scale_factor != 1:
        image = image.resize(new_size, resample=Image.BICUBIC)

    # Convert to grayscale
    image = image.convert("L")

    # Adaptive thresholding for better binarization
    try:
        import numpy as np
        arr = np.array(image)
        from PIL import ImageOps
        threshold = arr.mean() * 0.9
        binarized = Image.fromarray((arr > threshold).astype('uint8') * 255)
        image = binarized
    except ImportError:
        # Fallback to simple threshold if numpy not available
        image = image.point(lambda p: p > 128 and 255)

    # Enhance contrast
    enhancer = ImageEnhance.Contrast(image)
    image = enhancer.enhance(2.0)

    # Median filter to reduce noise
    image = image.filter(ImageFilter.MedianFilter())

    # Optionally set DPI metadata for downstream tools
    image.info['dpi'] = (300, 300)

    return image


def parse_cambodian_id_ocr(khmer_text: str, english_text: str) -> Dict[str, Optional[str]]:
    """Extract structured fields from OCR text using improved parsing for LM Studio structured output."""
    data = {
        "name": None,
        "name_kh": None,
        "name_en": None,
        "id_number": None,
        "dob": None,
        "nationality": "Cambodian",
        "gender": None,
        "address": None,
        "height": None,
        "birth_place": None,
        "issue_date": None,
        "expiry_date": None
    }

    # Combine both texts for comprehensive parsing
    combined_text = f"{khmer_text}\n{english_text}"

    # Logging raw OCR for debugging
    logger.info(f"Raw Combined OCR: {combined_text}")

    # Try to parse JSON format first (from enhanced LM Studio prompt)
    import json
    import re as regex

    # Look for JSON in the response - first check for "JSON:" section
    json_data = None
    json_section_start = combined_text.find("JSON:")
    if json_section_start != -1:
        # Extract text after "JSON:"
        json_text = combined_text[json_section_start + 5:].strip()

        # Find the JSON object
        start_idx = json_text.find('{')
        if start_idx != -1:
            # Find matching closing brace
            brace_count = 0
            end_idx = start_idx
            for i, char in enumerate(json_text[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break

            try:
                json_str = json_text[start_idx:end_idx]
                json_data = json.loads(json_str)
                logger.info("Successfully parsed JSON from 'JSON:' section")
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON from 'JSON:' section: {e}")

    # Fallback: look for any JSON pattern in the response
    if json_data is None:
        json_match = regex.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', combined_text, regex.DOTALL)
        if json_match:
            try:
                json_data = json.loads(json_match.group(0))
                logger.info("Successfully parsed JSON from fallback pattern")
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON from fallback pattern: {e}")

    if json_data:
        # Map JSON fields to our data structure
        if "id_number" in json_data and json_data["id_number"] not in ["", "Not Available", "Unclear"]:
            data["id_number"] = json_data["id_number"]
        if "name_kh" in json_data and json_data["name_kh"] not in ["", "Not Available", "Unclear"]:
            data["name_kh"] = json_data["name_kh"]
        if "name_en" in json_data and json_data["name_en"] not in ["", "Not Available", "Unclear"]:
            data["name_en"] = json_data["name_en"]
        if "birthdate" in json_data and json_data["birthdate"] not in ["", "Not Available", "Unclear"]:
            data["dob"] = json_data["birthdate"]
        if "gender" in json_data and json_data["gender"] not in ["", "Not Available", "Unclear"]:
            data["gender"] = json_data["gender"]
        if "nationality" in json_data and json_data["nationality"] not in ["", "Not Available", "Unclear"]:
            data["nationality"] = json_data["nationality"]
        if "address" in json_data and json_data["address"] not in ["", "Not Available", "Unclear"]:
            data["address"] = json_data["address"]
        if "height" in json_data and json_data["height"] not in ["", "Not Available", "Unclear"]:
            data["height"] = json_data["height"]
        if "birth_place" in json_data and json_data["birth_place"] not in ["", "Not Available", "Unclear"]:
            data["birth_place"] = json_data["birth_place"]
        if "issue_date" in json_data and json_data["issue_date"] not in ["", "Not Available", "Unclear"]:
            data["issue_date"] = json_data["issue_date"]
        if "expiry_date" in json_data and json_data["expiry_date"] not in ["", "Not Available", "Unclear"]:
            data["expiry_date"] = json_data["expiry_date"]

        logger.info(f"Extracted data from JSON: {data}")

    # Fallback to structured text patterns (from improved LM Studio prompt)
    structured_patterns = {
        "name_kh": r"Name \(Khmer\):\s*([^\n]+)",
        "name_en": r"Name \(English\):\s*([^\n]+)",
        "id_number": r"ID Number:\s*(\d{12})",
        "dob": r"Date of Birth:\s*([^\n]+)",
        "gender": r"Gender:\s*([^\n]+)",
        "nationality": r"Nationality:\s*([^\n]+)",
        "address": r"Address:\s*([^\n]+)",
        "height": r"Height:\s*([^\n]+)",
        "birth_place": r"Birth Place:\s*([^\n]+)",
        "issue_date": r"Issue Date:\s*([^\n]+)",
        "expiry_date": r"Expiry Date:\s*([^\n]+)"
    }

    # Parse structured format
    for field, pattern in structured_patterns.items():
        match = re.search(pattern, combined_text, re.IGNORECASE)
        if match:
            value = match.group(1).strip()
            if value and value.lower() not in ["not visible", "unclear", "n/a", ""]:
                data[field] = value
                logger.info(f"Extracted {field}: {value}")

    # Fallback to traditional regex patterns if structured parsing didn't work
    if not data["name_kh"] and not data["name_en"]:
        # Khmer name extraction (Unicode range)
        name_match = re.search(r"(?:ឈ្មោះ|Name)[^\S\r\n:]*([\u1780-\u17FF\s]+)", combined_text)
        if name_match:
            data["name_kh"] = name_match.group(1).strip()
            logger.info(f"Fallback Khmer name: {data['name_kh']}")

        # English name extraction
        name_match_en = re.search(r"(?:Name)[^\S\r\n:]*([A-Za-z\s]+)", combined_text)
        if name_match_en:
            data["name_en"] = name_match_en.group(1).strip()
            logger.info(f"Fallback English name: {data['name_en']}")

    if not data["id_number"]:
        # ID Number extraction
        id_match = re.search(r"(?:លេខសម្គាល់|ID)[^\d]*(\d{12})", combined_text)
        if not id_match:
            id_match = re.search(r"\b\d{12}\b", combined_text)
        if id_match:
            data["id_number"] = id_match.group(1) if hasattr(id_match.group(1), 'strip') else id_match.group(0)
            logger.info(f"Fallback ID: {data['id_number']}")

    if not data["dob"]:
        # Date of Birth extraction
        dob_match = re.search(r"(?:ថ្ងៃកំណើត|Date of Birth|DOB)[^\d]*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})", combined_text)
        if dob_match:
            data["dob"] = dob_match.group(1)
            logger.info(f"Fallback DOB: {data['dob']}")

    if not data["gender"]:
        # Gender extraction
        gender_match_kh = re.search(r"(?:ភេទ|Sex)[^\S\r\n:]*([\u1797\u179C]+|ប្រុស|ស្រី)", combined_text)
        gender_match_en = re.search(r"(?:Sex|Gender)[^\S\r\n:]*([MF]|Male|Female)", combined_text, re.IGNORECASE)

        if gender_match_kh:
            gender_kh = gender_match_kh.group(1)
            if 'ប្រុស' in gender_kh or gender_kh == '\u1797':
                data["gender"] = "Male"
            elif 'ស្រី' in gender_kh or gender_kh == '\u179C':
                data["gender"] = "Female"
            logger.info(f"Fallback Khmer gender: {data['gender']}")
        elif gender_match_en:
            gender_en = gender_match_en.group(1).upper()
            if gender_en in ["M", "MALE"]:
                data["gender"] = "Male"
            elif gender_en in ["F", "FEMALE"]:
                data["gender"] = "Female"
            logger.info(f"Fallback English gender: {data['gender']}")

    # Set combined name field for backward compatibility
    if data["name_kh"] and data["name_en"]:
        data["name"] = f"{data['name_kh']} / {data['name_en']}"
    elif data["name_kh"]:
        data["name"] = data["name_kh"]
    elif data["name_en"]:
        data["name"] = data["name_en"]

    return data
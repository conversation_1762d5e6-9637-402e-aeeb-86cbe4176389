#!/usr/bin/env python3
"""
Test script for vision models with Cambodian ID card OCR.
Tests if the loaded vision model can handle image input and produce JSON output.
"""

import lmstudio as lms
import json
import time
import base64
import io
from PIL import Image
from typing import Dict, Any, Optional

# Simple prompt for vision models
VISION_PROMPT = """You are an OCR system. Extract data from this Cambodian ID card image.

Return ONLY this JSON format:
{
  "id_number": "",
  "name_kh": "",
  "name_en": "",
  "birthdate": "",
  "gender": "",
  "nationality": "",
  "address": "",
  "height": "",
  "birth_place": "",
  "issue_date": "",
  "expiry_date": ""
}

Fill in the values from the ID card. Use empty strings for missing data."""


def test_vision_model_capabilities():
    """Test if the current model supports vision input."""
    print("🔍 Testing Vision Model Capabilities")
    print("=" * 50)
    
    try:
        # Initialize LM Studio model
        model = lms.llm()
        print("✅ Model loaded successfully")
        
        # Test basic text response first
        chat = lms.Chat()
        chat.add_user_message("Hello, can you see images?")
        
        response = model.respond(chat)
        print(f"📝 Model response: {str(response)[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {str(e)}")
        return False


def create_test_image() -> Image.Image:
    """Create a simple test image with Cambodian ID card-like text."""
    from PIL import Image, ImageDraw, ImageFont
    
    # Create a simple image
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    # Add some test text (simulating ID card)
    try:
        # Try to use a default font
        font = ImageFont.load_default()
    except:
        font = None
    
    test_text = [
        "គោត្តនាមនឹងនាម: សុខ វិចិត្រា",
        "Name: SOKH VICHETRA", 
        "លេខសម្គាល់: 123456789012",
        "ID: 123456789012",
        "ថ្ងៃកំណើត: 15/03/1990",
        "Date of Birth: 15/03/1990"
    ]
    
    y_position = 50
    for line in test_text:
        draw.text((20, y_position), line, fill='black', font=font)
        y_position += 30
    
    return img


def image_to_base64(image: Image.Image) -> str:
    """Convert PIL Image to base64 string."""
    buffer = io.BytesIO()
    if image.mode != 'RGB':
        image = image.convert('RGB')
    image.save(buffer, format='JPEG', quality=95)
    img_bytes = buffer.getvalue()
    return base64.b64encode(img_bytes).decode('utf-8')


def test_with_image(image_path: Optional[str] = None):
    """Test vision model with an image."""
    print("\n🖼️  Testing Vision Model with Image")
    print("=" * 50)
    
    try:
        # Load or create test image
        if image_path and os.path.exists(image_path):
            image = Image.open(image_path)
            print(f"📸 Loaded image: {image_path}")
        else:
            image = create_test_image()
            print("📸 Created test image with Cambodian text")
        
        print(f"📏 Image size: {image.size}, mode: {image.mode}")
        
        # Initialize model
        model = lms.llm()
        
        # Create chat with vision prompt
        chat = lms.Chat()
        
        # Note: The exact method for adding images depends on your LM Studio version
        # This is a placeholder - you may need to adjust based on your setup
        chat.add_user_message(VISION_PROMPT)
        
        print("📤 Sending image and prompt to model...")
        start_time = time.time()
        
        # For vision models, you might need to use a different method
        # This depends on how your LM Studio is configured
        response = model.respond(chat)
        
        processing_time = time.time() - start_time
        response_text = str(response).strip()
        
        print(f"✅ Response received in {processing_time:.2f} seconds")
        print(f"📏 Response length: {len(response_text)} characters")
        
        # Try to extract JSON
        json_data = extract_json_from_response(response_text)
        
        if json_data:
            print("✅ Successfully extracted JSON!")
            print("📋 Extracted data:")
            for key, value in json_data.items():
                print(f"   {key}: {value}")
            return True
        else:
            print("❌ Failed to extract valid JSON")
            print("📝 Raw response:")
            print(response_text)
            return False
        
    except Exception as e:
        print(f"❌ Error testing with image: {str(e)}")
        print("💡 This might be normal if:")
        print("   • Your model doesn't support vision input")
        print("   • LM Studio needs different image handling")
        print("   • The model needs specific image format")
        return False


def extract_json_from_response(response_text: str) -> Optional[Dict[str, Any]]:
    """Extract JSON from response."""
    try:
        # Look for JSON object
        start_idx = response_text.find('{')
        if start_idx != -1:
            brace_count = 0
            end_idx = start_idx
            for i, char in enumerate(response_text[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break
            
            json_str = response_text[start_idx:end_idx]
            return json.loads(json_str)
        
        return None
        
    except json.JSONDecodeError:
        return None


def get_model_info():
    """Get information about the currently loaded model."""
    print("\n📊 Model Information")
    print("=" * 50)
    
    try:
        # This is model-specific and may not work with all setups
        print("💡 To check your model details:")
        print("   • Open LM Studio interface")
        print("   • Check the 'My Models' tab")
        print("   • Look for vision capabilities in model description")
        print("   • Models with 'llava', 'vision', or 'multimodal' usually support images")
        
    except Exception as e:
        print(f"ℹ️  Could not get model info automatically: {e}")


def main():
    """Main test function."""
    print("🚀 Vision Model Testing for Cambodian ID Card OCR")
    print("=" * 60)
    
    # Test 1: Basic model capabilities
    if not test_vision_model_capabilities():
        print("❌ Basic model test failed. Check LM Studio connection.")
        return
    
    # Test 2: Model information
    get_model_info()
    
    # Test 3: Vision capabilities (if applicable)
    print("\n" + "="*60)
    print("🔍 VISION TEST")
    print("Note: This test may fail if your model doesn't support vision input")
    print("That's okay - you can still use text-only OCR with external tools")
    
    vision_success = test_with_image("id_card.jpg")  # Try with your test image
    
    # Summary and recommendations
    print("\n" + "="*60)
    print("📋 SUMMARY & RECOMMENDATIONS")
    print("="*60)
    
    if vision_success:
        print("✅ Your model supports vision input!")
        print("💡 Recommendations:")
        print("   • Use the vision-based OCR approach")
        print("   • Test with real Cambodian ID card images")
        print("   • Fine-tune the prompt for better accuracy")
    else:
        print("ℹ️  Vision input not working or not supported")
        print("💡 Alternative approaches:")
        print("   • Use PaddleOCR + text-only LLM")
        print("   • Try a different vision model (LLaVA, Moondream)")
        print("   • Use the existing text-based OCR pipeline")
    
    print("\n🎯 Recommended Models for 8GB GPU:")
    print("   • LLaVA-1.6-Mistral-7B (best vision + multilingual)")
    print("   • LLaVA-1.5-7B (stable, proven)")
    print("   • Moondream2 (lightweight, efficient)")
    
    print("\n✨ Test completed!")


if __name__ == "__main__":
    import os
    main()

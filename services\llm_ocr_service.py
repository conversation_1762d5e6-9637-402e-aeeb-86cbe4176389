import lmstudio as lms
import base64
import io
import logging
import time
import psutil
import os
from PIL import Image
from typing import Optional, Dict, Any
from fastapi import HTTPException

from config.llm_config import LMStudioConfig, instructor_rate_limiter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("LLMOCRService")

class LMStudioOCRService:
    """
    OCR service using LM Studio SDK with vision-capable LLM models.
    """

    def __init__(self,
                 model_name: Optional[str] = None,
                 host: str = "localhost:1234",
                 max_tokens: int = 2048,
                 temperature: float = 0.1):
        """
        Initialize LM Studio OCR service.

        Args:
            model_name: Name of the vision-capable model to use
            host: LM Studio server host and port
            max_tokens: Maximum tokens for response
            temperature: Temperature for text generation
        """
        self.model_name = model_name
        self.host = host
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.client = None
        self.model = None

    def _initialize_client(self):
        """Initialize LM Studio client and model."""
        try:
            # Method 1: Try creating a client directly (most reliable)
            logger.info(f"Attempting to connect to LM Studio at {self.host}")
            from lmstudio import Client

            # Create client instance with api_host parameter
            self.client = Client(api_host=self.host)

            # Get the LLM model
            if self.model_name:
                self.model = self.client.llm.model(self.model_name)
            else:
                # Use the default loaded model
                self.model = self.client.llm.model()

            logger.info(f"LM Studio client initialized successfully on {self.host}")
            return True

        except Exception as e:
            logger.error(f"Direct client initialization failed: {str(e)}")

            # Method 2: Try using the convenience API
            try:
                logger.info("Trying convenience API initialization...")

                # Reset any existing default client first
                try:
                    import lmstudio.sync_api as sync_api
                    sync_api._reset_default_client()
                except:
                    pass

                # Configure the default client with the specified host
                lms.configure_default_client(self.host)

                # Get the LLM model
                if self.model_name:
                    self.model = lms.llm(self.model_name)
                else:
                    # Use the default loaded model
                    self.model = lms.llm()

                logger.info("Convenience API initialization successful")
                return True

            except Exception as convenience_error:
                logger.error(f"Convenience API initialization failed: {convenience_error}")

                # Method 3: Try the simple approach without host specification
                try:
                    logger.info("Trying simple initialization (default host)...")
                    self.model = lms.llm()
                    logger.info("Simple initialization successful")
                    return True
                except Exception as simple_error:
                    logger.error(f"Simple initialization also failed: {simple_error}")
                    return False

    def _image_to_base64(self, image: Image.Image) -> str:
        """Convert PIL Image to base64 string."""
        buffer = io.BytesIO()
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        image.save(buffer, format='JPEG', quality=95)
        img_bytes = buffer.getvalue()
        return base64.b64encode(img_bytes).decode('utf-8')

    def _create_ocr_prompt(self, language_hint: str = "Khmer and English") -> str:
        """Create an enhanced prompt specifically for Cambodian ID card OCR."""
        return f"""You are an intelligent document extraction assistant designed specifically for parsing Cambodian ID cards.

TASK: Analyze the provided text/image of a Cambodian ID card and extract the following information into a well-structured JSON object.

FIELDS TO EXTRACT:
- id_number: The unique ID number on the card (e.g., IDKHM0703698038) - typically 12 digits
- passport_number: Passport number if available (e.g., 0207108F3009244KHM)
- name: Full name of the cardholder
- phone_number: Phone number associated with the ID, if visible
- address: Full address listed on the ID card
- birthdate: Date of birth as listed on the card (in original format if possible)
- gender: Gender inferred from name or explicitly stated
- nationality: Nationality inferred from ID format or explicitly stated

KHMER TEXT PATTERNS TO RECOGNIZE:
- ឈ្មោះ (Name) - Look for Khmer names using Unicode range ក-៹
- លេខសម្គាល់ (ID Number) - Usually followed by 12 digits
- ថ្ងៃកំណើត (Date of Birth) - Look for date patterns
- ភេទ (Gender) - ប្រុស (Male) or ស្រី (Female)
- សញ្ជាតិ (Nationality) - Usually ខ្មែរ (Khmer/Cambodian)
- អាសយដ្ឋាន (Address) - Full address information
- កម្ពស់ (Height) - Height information
- ទីកន្លែងកំណើត (Place of Birth) - Birth location

GUIDELINES:
- If any field is missing or unclear, leave it blank or note that it's "Not Available"
- Infer gender based on common naming conventions if not directly stated
- Infer nationality based on language and ID structure if not explicitly mentioned
- Maintain the original formatting of values unless otherwise specified
- Pay special attention to 12-digit ID numbers starting with common prefixes

OUTPUT FORMAT: Return structured JSON with extracted fields"""

    def _validate_request_limits(self, image: Image.Image, tokens_requested: int) -> None:
        """Validate request against configured limits."""
        # Get image size in bytes
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG')
        image_size = len(buffer.getvalue())

        # Get image format
        image_format = image.format or 'JPEG'

        # Validate basic limits
        is_valid, error_msg = LMStudioConfig.validate_request_limits(
            tokens_requested, image_size, image_format
        )
        if not is_valid:
            raise HTTPException(status_code=400, detail=f"Request validation failed: {error_msg}")

        # Check rate limits
        can_proceed, rate_limit_msg = instructor_rate_limiter.can_make_request()
        if not can_proceed:
            raise HTTPException(status_code=429, detail=f"Rate limit exceeded: {rate_limit_msg}")

        # Record request start
        if not instructor_rate_limiter.record_request_start(tokens_requested):
            raise HTTPException(status_code=429, detail="Daily token limit exceeded")

    def _monitor_resource_usage(self) -> Dict[str, Any]:
        """Monitor current resource usage."""
        if not LMStudioConfig.ENABLE_RESOURCE_MONITORING:
            return {}

        try:
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            cpu_percent = process.cpu_percent()

            # Check memory limit
            if memory_mb > LMStudioConfig.MAX_MEMORY_USAGE_MB:
                logger.warning(f"Memory usage ({memory_mb:.1f}MB) exceeds limit ({LMStudioConfig.MAX_MEMORY_USAGE_MB}MB)")

            return {
                "memory_usage_mb": round(memory_mb, 1),
                "cpu_percent": cpu_percent,
                "memory_limit_mb": LMStudioConfig.MAX_MEMORY_USAGE_MB,
            }
        except Exception as e:
            logger.error(f"Resource monitoring failed: {e}")
            return {}

    def extract_text_from_image(self, image: Image.Image, language_hint: str = "Khmer and English") -> Dict[str, Any]:
        """
        Extract text from image using LM Studio LLM with image description.
        Note: This is a simplified version that uses text-based LLM interaction.
        For full vision model support, ensure you have a vision-capable model loaded in LM Studio.

        Args:
            image: PIL Image object
            language_hint: Hint about expected languages in the image

        Returns:
            Dictionary containing extracted text and metadata
        """
        start_time = time.time()

        try:
            # Validate request limits before processing
            tokens_requested = min(self.max_tokens, LMStudioConfig.MAX_TOKENS_PER_REQUEST)
            self._validate_request_limits(image, tokens_requested)

            # Monitor resource usage
            resource_stats = self._monitor_resource_usage()

            # Initialize client if not already done
            if not self.model:
                if not self._initialize_client():
                    raise HTTPException(status_code=500, detail="Failed to initialize LM Studio client")

            # Enhanced prompt for Cambodian ID card OCR using LM Studio
            prompt = """You are an intelligent document extraction assistant designed specifically for parsing Cambodian ID cards.

ROLE: You are an expert OCR system with specialized knowledge of Cambodian ID card formats, Khmer script, and document structure.

TASK: Extract text from a Cambodian ID card and return it in structured JSON format.

FIELDS TO EXTRACT:
{
  "id_number": "The unique ID number (typically 12 digits, e.g., IDKHM0703698038)",
  "passport_number": "Passport number if available (e.g., 0207108F3009244KHM)",
  "name": "Full name of the cardholder",
  "name_kh": "Name in Khmer script",
  "name_en": "Name in English/Latin script",
  "phone_number": "Phone number if visible",
  "address": "Full address listed on the ID card",
  "birthdate": "Date of birth (maintain original format)",
  "gender": "Gender (Male/Female or ប្រុស/ស្រី)",
  "nationality": "Nationality (usually Cambodian/ខ្មែរ)",
  "height": "Height if visible",
  "birth_place": "Place of birth if visible",
  "issue_date": "Issue date if visible",
  "expiry_date": "Expiry date if visible"
}

KHMER TEXT RECOGNITION PATTERNS:
- ឈ្មោះ (Name) → Look for names in Khmer Unicode range ក-៹
- លេខសម្គាល់ (ID Number) → Usually 12 digits
- ថ្ងៃកំណើត (Date of Birth) → Date patterns DD/MM/YYYY or DD-MM-YYYY
- ភេទ (Gender) → ប្រុស (Male) or ស្រី (Female)
- សញ្ជាតិ (Nationality) → ខ្មែរ (Khmer/Cambodian)
- អាសយដ្ឋាន (Address) → Full address information
- កម្ពស់ (Height) → Height measurements
- ទីកន្លែងកំណើត (Place of Birth) → Birth location

EXTRACTION GUIDELINES:
1. Extract ALL visible text maintaining original formatting
2. If any field is missing/unclear, use "Not Available" or leave blank
3. Infer gender from naming conventions if not explicitly stated
4. Pay special attention to 12-digit ID numbers
5. Maintain original date formats when possible
6. Look for both Khmer and English text versions

RESPONSE FORMAT:
First provide raw extracted text, then structured JSON data:

RAW TEXT:
[All visible text from the image]

STRUCTURED JSON:
{
  "id_number": "extracted_id",
  "name_kh": "khmer_name",
  "name_en": "english_name",
  "birthdate": "birth_date",
  "gender": "gender",
  "nationality": "nationality",
  "address": "full_address",
  "height": "height_if_visible",
  "birth_place": "birth_place_if_visible",
  "issue_date": "issue_date_if_visible",
  "expiry_date": "expiry_date_if_visible"
}

Please analyze the image and extract all information following this format."""

            # Prepare the chat
            chat = lms.Chat()
            chat.add_user_message(prompt)

            # Make the prediction with timeout enforcement
            try:
                # Check processing time limit
                elapsed_time = time.time() - start_time
                if elapsed_time > LMStudioConfig.MAX_PROCESSING_TIME:
                    raise HTTPException(status_code=408, detail="Processing time limit exceeded")

                response = self.model.respond(chat)
                extracted_text = str(response).strip()

                # Enforce response size limit
                if len(extracted_text) > LMStudioConfig.MAX_RESPONSE_SIZE:
                    logger.warning(f"Response truncated from {len(extracted_text)} to {LMStudioConfig.MAX_RESPONSE_SIZE} characters")
                    extracted_text = extracted_text[:LMStudioConfig.MAX_RESPONSE_SIZE] + "... [truncated]"

            except Exception as model_error:
                logger.error(f"Model response error: {model_error}")
                # Fallback response
                extracted_text = f"LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: {image.size}, Mode: {image.mode}"

            # Calculate final processing time
            processing_time = time.time() - start_time

            logger.info(f"LM Studio service called successfully in {processing_time:.2f}s")

            # Get rate limiter stats
            rate_stats = instructor_rate_limiter.get_current_stats()

            return {
                "extracted_text": extracted_text,
                "model_used": self.model_name or "default",
                "confidence": 0.8,  # Lower confidence for placeholder implementation
                "method": "llm_text_based",
                "note": "This is a placeholder implementation. For full OCR, use a vision-capable model.",
                "processing_time": round(processing_time, 2),
                "resource_stats": resource_stats,
                "rate_limit_stats": rate_stats,
                "tokens_used": tokens_requested,
                "limits_enforced": {
                    "max_tokens": LMStudioConfig.MAX_TOKENS_PER_REQUEST,
                    "max_processing_time": LMStudioConfig.MAX_PROCESSING_TIME,
                    "max_response_size": LMStudioConfig.MAX_RESPONSE_SIZE,
                }
            }

        except HTTPException:
            # Re-raise HTTP exceptions (rate limits, validation errors)
            raise
        except Exception as e:
            logger.error(f"Error in LM Studio OCR: {str(e)}")
            raise HTTPException(status_code=500, detail=f"LM Studio OCR failed: {str(e)}")
        finally:
            # Always record request end for rate limiting
            instructor_rate_limiter.record_request_end()

    def extract_text_simple(self, image: Image.Image) -> str:
        """
        Simple text extraction that returns only the text string.

        Args:
            image: PIL Image object

        Returns:
            Extracted text as string
        """
        try:
            result = self.extract_text_from_image(image)
            return result.get("extracted_text", "")
        except Exception as e:
            logger.error(f"Simple text extraction failed: {str(e)}")
            return ""

# Global instance
llm_ocr_service = LMStudioOCRService()

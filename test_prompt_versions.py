#!/usr/bin/env python3
"""
Test different versions of the Cambodian ID card OCR prompt.
This helps find which prompt style works best with your specific model.
"""

import lmstudio as lms
import json
import time
from typing import Dict, Any

# Test data - sample Cambodian ID card text
SAMPLE_ID_CARD_TEXT = """
ឈ្មោះ: សុខ វិចិត្រា
Name: SOKH VICHETRA
លេខសម្គាល់: 123456789012
ID: 123456789012
ថ្ងៃកំណើត: 15/03/1990
Date of Birth: 15/03/1990
ភេទ: ស្រី
Sex: F
សញ្ជាតិ: ខ្មែរ
Nationality: Cambodian
អាសយដ្ឋាន: ភូមិ១ ឃុំ២ ស្រុក៣ ខេត្តកំពង់ចាម
Address: Village 1, Commune 2, District 3, Kampong Cham Province
កម្ពស់: 165cm
Height: 165cm
"""

# Version 1: Detailed prompt
PROMPT_V1_DETAILED = """You are an intelligent document extraction assistant designed specifically for parsing Cambodian ID cards.

ROLE: You are an expert OCR system with specialized knowledge of Cambodian ID card formats, Khmer script, and document structure.

TASK: Extract text from a Cambodian ID card and return it in structured JSON format.

FIELDS TO EXTRACT:
{
  "id_number": "The unique ID number (typically 12 digits)",
  "name_kh": "Name in Khmer script",
  "name_en": "Name in English/Latin script",
  "birthdate": "Date of birth (maintain original format)",
  "gender": "Gender (Male/Female)",
  "nationality": "Nationality (usually Cambodian/ខ្មែរ)",
  "address": "Full address listed on the ID card",
  "height": "Height if visible",
  "birth_place": "Place of birth if visible",
  "issue_date": "Issue date if visible",
  "expiry_date": "Expiry date if visible"
}

RESPONSE FORMAT:
You MUST respond with EXACTLY this format:

RAW TEXT:
[All visible text from the image]

JSON:
{
  "id_number": "extracted_id_number_here",
  "name_kh": "khmer_name_here",
  "name_en": "english_name_here",
  "birthdate": "birth_date_here",
  "gender": "gender_here",
  "nationality": "nationality_here",
  "address": "full_address_here",
  "height": "height_if_visible_or_empty",
  "birth_place": "birth_place_if_visible_or_empty",
  "issue_date": "issue_date_if_visible_or_empty",
  "expiry_date": "expiry_date_if_visible_or_empty"
}

IMPORTANT: You MUST include the JSON section with valid JSON syntax. Use empty strings "" for missing fields."""

# Version 2: Concise prompt
PROMPT_V2_CONCISE = """Extract information from this Cambodian ID card and return ONLY valid JSON:

{
  "id_number": "12-digit ID number",
  "name_kh": "Name in Khmer script",
  "name_en": "Name in English",
  "birthdate": "Date of birth",
  "gender": "Male or Female",
  "nationality": "Nationality",
  "address": "Full address",
  "height": "Height if visible",
  "birth_place": "Birth place if visible",
  "issue_date": "Issue date if visible",
  "expiry_date": "Expiry date if visible"
}

Instructions:
- Extract ALL visible text from the Cambodian ID card
- Look for Khmer text patterns: ឈ្មោះ (Name), លេខសម្គាល់ (ID), ថ្ងៃកំណើត (DOB), ភេទ (Gender), អាសយដ្ឋាន (Address)
- Use empty string "" for missing fields
- Return ONLY the JSON object, no other text"""

# Version 3: Simple prompt
PROMPT_V3_SIMPLE = """You are an OCR system. Extract data from this Cambodian ID card.

Return this exact JSON format:
{
  "id_number": "",
  "name_kh": "",
  "name_en": "",
  "birthdate": "",
  "gender": "",
  "nationality": "",
  "address": "",
  "height": "",
  "birth_place": "",
  "issue_date": "",
  "expiry_date": ""
}

Fill in the values from the ID card. Use empty strings for missing data."""


def test_prompt_version(prompt: str, version_name: str, test_text: str) -> Dict[str, Any]:
    """Test a specific prompt version."""
    print(f"\n🧪 Testing {version_name}")
    print("=" * 50)
    
    try:
        # Initialize LM Studio model
        model = lms.llm()
        
        # Combine prompt with test text
        full_prompt = f"{prompt}\n\nTEXT TO ANALYZE:\n{test_text}"
        
        # Create chat and get response
        chat = lms.Chat()
        chat.add_user_message(full_prompt)
        
        print("📤 Sending request to LM Studio...")
        start_time = time.time()
        
        response = model.respond(chat)
        
        processing_time = time.time() - start_time
        response_text = str(response).strip()
        
        print(f"✅ Response received in {processing_time:.2f} seconds")
        print(f"📏 Response length: {len(response_text)} characters")
        
        # Try to extract JSON
        json_data = extract_json_from_response(response_text)
        
        result = {
            "version": version_name,
            "processing_time": processing_time,
            "response_length": len(response_text),
            "response_text": response_text,
            "json_extracted": json_data is not None,
            "json_data": json_data,
            "success": json_data is not None and len(json_data) > 0
        }
        
        if json_data:
            print("✅ Successfully extracted JSON!")
            print("📋 Extracted data:")
            for key, value in json_data.items():
                print(f"   {key}: {value}")
        else:
            print("❌ Failed to extract valid JSON")
            print("📝 Raw response:")
            print(response_text[:200] + "..." if len(response_text) > 200 else response_text)
        
        return result
        
    except Exception as e:
        print(f"❌ Error testing {version_name}: {str(e)}")
        return {
            "version": version_name,
            "error": str(e),
            "success": False
        }


def extract_json_from_response(response_text: str) -> Dict[str, Any]:
    """Extract JSON from LLM response using multiple strategies."""
    try:
        # Strategy 1: Look for "JSON:" section
        json_section_start = response_text.find("JSON:")
        if json_section_start != -1:
            json_text = response_text[json_section_start + 5:].strip()
            start_idx = json_text.find('{')
            if start_idx != -1:
                brace_count = 0
                end_idx = start_idx
                for i, char in enumerate(json_text[start_idx:], start_idx):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_idx = i + 1
                            break
                
                json_str = json_text[start_idx:end_idx]
                return json.loads(json_str)
        
        # Strategy 2: Look for first complete JSON object
        start_idx = response_text.find('{')
        if start_idx != -1:
            brace_count = 0
            end_idx = start_idx
            for i, char in enumerate(response_text[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break
            
            json_str = response_text[start_idx:end_idx]
            return json.loads(json_str)
        
        return None
        
    except json.JSONDecodeError:
        return None


def main():
    """Main test function."""
    print("🚀 Cambodian ID Card OCR Prompt Version Testing")
    print("=" * 60)
    print("Testing different prompt styles to find the best one for your model")
    
    results = []
    
    # Test all prompt versions
    test_cases = [
        (PROMPT_V1_DETAILED, "Version 1 (Detailed)"),
        (PROMPT_V2_CONCISE, "Version 2 (Concise)"),
        (PROMPT_V3_SIMPLE, "Version 3 (Simple)")
    ]
    
    for prompt, version_name in test_cases:
        result = test_prompt_version(prompt, version_name, SAMPLE_ID_CARD_TEXT)
        results.append(result)
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n📊 SUMMARY RESULTS")
    print("=" * 60)
    
    successful_versions = [r for r in results if r.get("success", False)]
    
    if successful_versions:
        print("✅ Successful versions:")
        for result in successful_versions:
            print(f"   • {result['version']}: {result['processing_time']:.2f}s")
        
        # Recommend best version
        best_version = min(successful_versions, key=lambda x: x['processing_time'])
        print(f"\n🏆 RECOMMENDED: {best_version['version']}")
        print(f"   ⚡ Fastest processing time: {best_version['processing_time']:.2f}s")
        print(f"   📋 Successfully extracted {len(best_version['json_data'])} fields")
        
    else:
        print("❌ No versions successfully extracted JSON")
        print("💡 Suggestions:")
        print("   • Try a different model in LM Studio")
        print("   • Check if the model supports structured output")
        print("   • Verify LM Studio is running and accessible")
    
    print("\n✨ Testing completed!")
    
    # Save results to file
    with open("prompt_test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print("📁 Results saved to prompt_test_results.json")


if __name__ == "__main__":
    main()

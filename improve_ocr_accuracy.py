#!/usr/bin/env python3
"""
Script to improve OCR accuracy for Cambodian ID cards.
Tests different approaches and settings to get better results.
"""

import lmstudio as lms
import json
import time
from PIL import Image, ImageEnhance, ImageFilter
import io
import base64
from typing import Dict, Any, Optional

# Optimized prompt based on your test results
OPTIMIZED_PROMPT = """You are an expert OCR system for Cambodian ID cards. Look carefully at this image and extract ALL visible text.

IMPORTANT: Read the image very carefully. Look for both Khmer (Cambodian) and English text.

LOOK FOR THESE PATTERNS:
- ឈ្មោះ / Name: Person's name in Khmer and English
- លេខសម្គាល់ / ID: Usually 12 digits, sometimes with letters  
- ថ្ងៃកំណើត / Date of Birth: DD/MM/YYYY format
- ភេទ / Sex: ប្រុស (Male) / ស្រី (Female) or M/F
- សញ្ជាតិ / Nationality: Usually ខ្មែរ or Cambodian
- អាសយដ្ឋាន / Address: Full address text
- កម្ពស់ / Height: Height measurement
- ថ្ងៃចេញ / Issue Date: When card was issued
- ថ្ងៃផុតកំណត់ / Expiry Date: When card expires

Return ONLY this JSON format:
{
  "id_number": "",
  "name_kh": "",
  "name_en": "",
  "birthdate": "",
  "gender": "",
  "nationality": "",
  "address": "",
  "height": "",
  "birth_place": "",
  "issue_date": "",
  "expiry_date": ""
}

Fill in ALL fields you can see. Use empty string "" for missing data."""

# Alternative simpler prompt
SIMPLE_PROMPT = """Look at this Cambodian ID card image. Extract the text and return JSON:

{
  "id_number": "ID number from the card",
  "name_kh": "Khmer name",
  "name_en": "English name", 
  "birthdate": "birth date",
  "gender": "Male or Female",
  "nationality": "nationality",
  "address": "address text",
  "height": "height if visible",
  "birth_place": "birth place if visible",
  "issue_date": "issue date if visible",
  "expiry_date": "expiry date if visible"
}

Read carefully and fill in what you can see."""


def enhance_image_for_ocr(image: Image.Image, enhancement_type: str = "standard") -> Image.Image:
    """Apply different image enhancements to improve OCR accuracy."""
    
    if enhancement_type == "standard":
        # Standard enhancement
        image = image.convert("RGB")
        
        # Increase contrast
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.5)
        
        # Increase sharpness
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.2)
        
        return image
    
    elif enhancement_type == "high_contrast":
        # High contrast for better text recognition
        image = image.convert("L")  # Grayscale
        
        # Apply threshold
        threshold = 128
        image = image.point(lambda p: 255 if p > threshold else 0, mode='1')
        image = image.convert("RGB")
        
        return image
    
    elif enhancement_type == "denoised":
        # Noise reduction
        image = image.convert("RGB")
        
        # Apply median filter to reduce noise
        image = image.filter(ImageFilter.MedianFilter(size=3))
        
        # Enhance contrast
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.3)
        
        return image
    
    else:
        return image


def test_ocr_with_settings(image_path: str, prompt: str, enhancement: str = "standard", 
                          temperature: float = 0.1) -> Dict[str, Any]:
    """Test OCR with specific settings."""
    
    try:
        # Load and enhance image
        image = Image.open(image_path)
        enhanced_image = enhance_image_for_ocr(image, enhancement)
        
        print(f"🔧 Testing with enhancement: {enhancement}, temperature: {temperature}")
        
        # Initialize model
        model = lms.llm()
        
        # Create chat
        chat = lms.Chat()
        chat.add_user_message(prompt)
        
        start_time = time.time()
        response = model.respond(chat)
        processing_time = time.time() - start_time
        
        response_text = str(response).strip()
        
        # Extract JSON
        json_data = extract_json_from_response(response_text)
        
        # Count extracted fields
        filled_fields = 0
        if json_data:
            filled_fields = sum(1 for v in json_data.values() if v and v.strip() and v != "Not Available")
        
        return {
            "enhancement": enhancement,
            "temperature": temperature,
            "processing_time": processing_time,
            "json_extracted": json_data is not None,
            "filled_fields": filled_fields,
            "json_data": json_data,
            "response_text": response_text
        }
        
    except Exception as e:
        return {
            "enhancement": enhancement,
            "temperature": temperature,
            "error": str(e),
            "json_extracted": False,
            "filled_fields": 0
        }


def extract_json_from_response(response_text: str) -> Optional[Dict[str, Any]]:
    """Extract JSON from response."""
    try:
        start_idx = response_text.find('{')
        if start_idx != -1:
            brace_count = 0
            end_idx = start_idx
            for i, char in enumerate(response_text[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break
            
            json_str = response_text[start_idx:end_idx]
            return json.loads(json_str)
        
        return None
        
    except json.JSONDecodeError:
        return None


def run_accuracy_tests(image_path: str = "id_card.jpg"):
    """Run comprehensive accuracy tests."""
    print("🎯 OCR Accuracy Improvement Tests")
    print("=" * 60)
    
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        print("Please place your Cambodian ID card image as 'id_card.jpg'")
        return
    
    # Test configurations
    test_configs = [
        {"prompt": OPTIMIZED_PROMPT, "enhancement": "standard", "temperature": 0.1},
        {"prompt": OPTIMIZED_PROMPT, "enhancement": "high_contrast", "temperature": 0.1},
        {"prompt": OPTIMIZED_PROMPT, "enhancement": "denoised", "temperature": 0.1},
        {"prompt": SIMPLE_PROMPT, "enhancement": "standard", "temperature": 0.1},
        {"prompt": OPTIMIZED_PROMPT, "enhancement": "standard", "temperature": 0.05},
    ]
    
    results = []
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n🧪 Test {i}/{len(test_configs)}")
        print("-" * 40)
        
        result = test_ocr_with_settings(
            image_path, 
            config["prompt"], 
            config["enhancement"], 
            config["temperature"]
        )
        
        results.append(result)
        
        if result.get("json_extracted"):
            print(f"✅ Success: {result['filled_fields']} fields extracted")
            if result["json_data"]:
                for key, value in result["json_data"].items():
                    if value and value.strip():
                        print(f"   {key}: {value}")
        else:
            print("❌ Failed to extract JSON")
            if "error" in result:
                print(f"   Error: {result['error']}")
        
        time.sleep(1)  # Brief pause between tests
    
    # Find best result
    successful_results = [r for r in results if r.get("json_extracted", False)]
    
    if successful_results:
        best_result = max(successful_results, key=lambda x: x["filled_fields"])
        
        print("\n🏆 BEST RESULT")
        print("=" * 60)
        print(f"Enhancement: {best_result['enhancement']}")
        print(f"Temperature: {best_result['temperature']}")
        print(f"Fields extracted: {best_result['filled_fields']}")
        print(f"Processing time: {best_result['processing_time']:.2f}s")
        
        print("\n📋 Extracted Data:")
        if best_result["json_data"]:
            for key, value in best_result["json_data"].items():
                print(f"   {key}: {value}")
        
        # Save best configuration
        best_config = {
            "enhancement": best_result["enhancement"],
            "temperature": best_result["temperature"],
            "prompt": "optimized" if best_result in results[:4] else "simple",
            "extracted_data": best_result["json_data"]
        }
        
        with open("best_ocr_config.json", "w", encoding="utf-8") as f:
            json.dump(best_config, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Best configuration saved to 'best_ocr_config.json'")
        
    else:
        print("\n❌ No successful extractions")
        print("💡 Suggestions:")
        print("   • Try a different vision model")
        print("   • Check image quality")
        print("   • Verify the ID card is clearly visible")
    
    print("\n✨ Testing completed!")


def analyze_current_results():
    """Analyze the results you shared."""
    print("📊 Analysis of Your Current Results")
    print("=" * 50)
    
    current_results = {
        "id_number": "4803614",  # Partial - should be 12 digits
        "name_kh": "សតិធវ នារីឥណ",  # Detected but may have OCR errors
        "name_en": "SATY NARIN",  # Good detection
        "birthdate": "",  # Missing
        "gender": "",  # Missing
        "nationality": "",  # Missing
        "address": "ក្រដា៊ករុបខររភឹទលធបផល",  # Detected but garbled
        "height": "",  # Missing
        "birth_place": "",  # Missing
        "issue_date": "05/04/2018",  # Good detection
        "expiry_date": "",  # Missing
    }
    
    filled_fields = sum(1 for v in current_results.values() if v)
    total_fields = len(current_results)
    
    print(f"✅ Fields detected: {filled_fields}/{total_fields}")
    print(f"📈 Success rate: {(filled_fields/total_fields)*100:.1f}%")
    
    print("\n🎯 Areas for improvement:")
    missing_fields = [k for k, v in current_results.items() if not v]
    for field in missing_fields:
        print(f"   • {field}")
    
    print("\n💡 Recommendations:")
    print("   • Image enhancement may help with missing fields")
    print("   • Lower temperature (0.05) for more consistent output")
    print("   • Try different prompt styles")
    print("   • Check if text is clearly visible in the image")


def main():
    """Main function."""
    import os
    
    print("🚀 Cambodian ID Card OCR Accuracy Improvement")
    print("=" * 60)
    
    # Analyze current results
    analyze_current_results()
    
    # Run improvement tests
    print("\n" + "="*60)
    run_accuracy_tests()


if __name__ == "__main__":
    import os
    main()

import os
from typing import Optional
import time
from threading import Lock

class LMStudioConfig:
    """Configuration for LM Studio OCR service."""

    # LM Studio server settings
    HOST: str = os.getenv("LMSTUDIO_HOST", "localhost:1234")

    # Model settings
    DEFAULT_MODEL: Optional[str] = os.getenv("LMSTUDIO_MODEL", None)

    # OCR specific settings
    MAX_TOKENS: int = int(os.getenv("LMSTUDIO_MAX_TOKENS", "2048"))
    TEMPERATURE: float = float(os.getenv("LMSTUDIO_TEMPERATURE", "0.1"))

    # Image processing settings
    MAX_IMAGE_SIZE: tuple = (2048, 2048)  # Max dimensions for image processing
    IMAGE_QUALITY: int = 95  # JPEG quality for image encoding

    # Timeout settings
    REQUEST_TIMEOUT: int = int(os.getenv("LMSTUDIO_TIMEOUT", "60"))  # seconds

    # Fallback settings
    ENABLE_TESSERACT_FALLBACK: bool = os.getenv("ENABLE_TESSERACT_FALLBACK", "false").lower() == "true"

    # Instructor/LLM Limitation Settings
    # Token limits
    MIN_TOKENS: int = int(os.getenv("LMSTUDIO_MIN_TOKENS", "50"))  # Minimum tokens per request
    MAX_TOKENS_PER_REQUEST: int = int(os.getenv("LMSTUDIO_MAX_TOKENS_PER_REQUEST", "4096"))  # Maximum tokens per single request
    MAX_DAILY_TOKENS: int = int(os.getenv("LMSTUDIO_MAX_DAILY_TOKENS", "100000"))  # Daily token limit

    # Rate limiting
    MAX_REQUESTS_PER_MINUTE: int = int(os.getenv("LMSTUDIO_MAX_REQUESTS_PER_MINUTE", "10"))  # Rate limit per minute
    MAX_REQUESTS_PER_HOUR: int = int(os.getenv("LMSTUDIO_MAX_REQUESTS_PER_HOUR", "100"))  # Rate limit per hour
    MAX_CONCURRENT_REQUESTS: int = int(os.getenv("LMSTUDIO_MAX_CONCURRENT_REQUESTS", "3"))  # Concurrent request limit

    # Processing limits
    MAX_PROCESSING_TIME: int = int(os.getenv("LMSTUDIO_MAX_PROCESSING_TIME", "120"))  # Max processing time in seconds
    MAX_RESPONSE_SIZE: int = int(os.getenv("LMSTUDIO_MAX_RESPONSE_SIZE", "10240"))  # Max response size in characters
    MAX_IMAGE_FILE_SIZE: int = int(os.getenv("LMSTUDIO_MAX_IMAGE_FILE_SIZE", "10485760"))  # Max image size in bytes (10MB)

    # Content filtering
    ENABLE_CONTENT_FILTER: bool = os.getenv("LMSTUDIO_ENABLE_CONTENT_FILTER", "true").lower() == "true"
    ALLOWED_IMAGE_FORMATS: list = ["JPEG", "PNG", "WEBP", "BMP"]  # Allowed image formats
    BLOCKED_KEYWORDS: list = []  # Keywords to block in prompts (can be extended)

    # Memory and resource limits
    MAX_MEMORY_USAGE_MB: int = int(os.getenv("LMSTUDIO_MAX_MEMORY_USAGE_MB", "512"))  # Max memory usage in MB
    ENABLE_RESOURCE_MONITORING: bool = os.getenv("LMSTUDIO_ENABLE_RESOURCE_MONITORING", "true").lower() == "true"

    @classmethod
    def get_model_config(cls) -> dict:
        """Get model configuration for LM Studio."""
        return {
            "maxTokens": min(cls.MAX_TOKENS, cls.MAX_TOKENS_PER_REQUEST),  # Enforce token limits
            "temperature": cls.TEMPERATURE,
        }

    @classmethod
    def get_connection_config(cls) -> dict:
        """Get connection configuration for LM Studio."""
        return {
            "host": cls.HOST,
            "timeout": min(cls.REQUEST_TIMEOUT, cls.MAX_PROCESSING_TIME),  # Enforce processing time limits
        }

    @classmethod
    def get_instructor_limits(cls) -> dict:
        """Get instructor limitation configuration."""
        return {
            "token_limits": {
                "min_tokens": cls.MIN_TOKENS,
                "max_tokens_per_request": cls.MAX_TOKENS_PER_REQUEST,
                "max_daily_tokens": cls.MAX_DAILY_TOKENS,
            },
            "rate_limits": {
                "max_requests_per_minute": cls.MAX_REQUESTS_PER_MINUTE,
                "max_requests_per_hour": cls.MAX_REQUESTS_PER_HOUR,
                "max_concurrent_requests": cls.MAX_CONCURRENT_REQUESTS,
            },
            "processing_limits": {
                "max_processing_time": cls.MAX_PROCESSING_TIME,
                "max_response_size": cls.MAX_RESPONSE_SIZE,
                "max_image_file_size": cls.MAX_IMAGE_FILE_SIZE,
            },
            "content_filter": {
                "enabled": cls.ENABLE_CONTENT_FILTER,
                "allowed_image_formats": cls.ALLOWED_IMAGE_FORMATS,
                "blocked_keywords": cls.BLOCKED_KEYWORDS,
            },
            "resource_limits": {
                "max_memory_usage_mb": cls.MAX_MEMORY_USAGE_MB,
                "enable_monitoring": cls.ENABLE_RESOURCE_MONITORING,
            }
        }

    @classmethod
    def validate_request_limits(cls, tokens_requested: int, image_size: int, image_format: str) -> tuple[bool, str]:
        """
        Validate if a request meets the configured limits.

        Args:
            tokens_requested: Number of tokens requested
            image_size: Size of image in bytes
            image_format: Format of the image

        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check token limits
        if tokens_requested < cls.MIN_TOKENS:
            return False, f"Token request too small. Minimum: {cls.MIN_TOKENS}, requested: {tokens_requested}"

        if tokens_requested > cls.MAX_TOKENS_PER_REQUEST:
            return False, f"Token request too large. Maximum: {cls.MAX_TOKENS_PER_REQUEST}, requested: {tokens_requested}"

        # Check image size
        if image_size > cls.MAX_IMAGE_FILE_SIZE:
            return False, f"Image file too large. Maximum: {cls.MAX_IMAGE_FILE_SIZE} bytes, provided: {image_size} bytes"

        # Check image format
        if cls.ENABLE_CONTENT_FILTER and image_format.upper() not in cls.ALLOWED_IMAGE_FORMATS:
            return False, f"Image format not allowed. Allowed formats: {cls.ALLOWED_IMAGE_FORMATS}, provided: {image_format}"

        return True, ""


class InstructorRateLimiter:
    """Rate limiter for LM Studio instructor requests."""

    def __init__(self):
        self._request_times = []
        self._hourly_request_times = []
        self._daily_token_count = 0
        self._daily_reset_time = time.time()
        self._concurrent_requests = 0
        self._lock = Lock()

    def can_make_request(self) -> tuple[bool, str]:
        """
        Check if a new request can be made based on rate limits.

        Returns:
            Tuple of (can_make_request, reason_if_not)
        """
        with self._lock:
            current_time = time.time()

            # Check concurrent requests
            if self._concurrent_requests >= LMStudioConfig.MAX_CONCURRENT_REQUESTS:
                return False, f"Too many concurrent requests. Maximum: {LMStudioConfig.MAX_CONCURRENT_REQUESTS}"

            # Clean old request times (older than 1 minute)
            self._request_times = [t for t in self._request_times if current_time - t < 60]

            # Check per-minute rate limit
            if len(self._request_times) >= LMStudioConfig.MAX_REQUESTS_PER_MINUTE:
                return False, f"Rate limit exceeded. Maximum: {LMStudioConfig.MAX_REQUESTS_PER_MINUTE} requests per minute"

            # Clean old hourly request times (older than 1 hour)
            self._hourly_request_times = [t for t in self._hourly_request_times if current_time - t < 3600]

            # Check per-hour rate limit
            if len(self._hourly_request_times) >= LMStudioConfig.MAX_REQUESTS_PER_HOUR:
                return False, f"Hourly rate limit exceeded. Maximum: {LMStudioConfig.MAX_REQUESTS_PER_HOUR} requests per hour"

            return True, ""

    def record_request_start(self, tokens_requested: int) -> bool:
        """
        Record the start of a request.

        Args:
            tokens_requested: Number of tokens for this request

        Returns:
            True if request can proceed, False if daily token limit exceeded
        """
        with self._lock:
            current_time = time.time()

            # Reset daily counters if it's a new day
            if current_time - self._daily_reset_time > 86400:  # 24 hours
                self._daily_token_count = 0
                self._daily_reset_time = current_time

            # Check daily token limit
            if self._daily_token_count + tokens_requested > LMStudioConfig.MAX_DAILY_TOKENS:
                return False

            # Record the request
            self._request_times.append(current_time)
            self._hourly_request_times.append(current_time)
            self._daily_token_count += tokens_requested
            self._concurrent_requests += 1

            return True

    def record_request_end(self):
        """Record the end of a request."""
        with self._lock:
            if self._concurrent_requests > 0:
                self._concurrent_requests -= 1

    def get_current_stats(self) -> dict:
        """Get current rate limiting statistics."""
        with self._lock:
            current_time = time.time()

            # Clean old times for accurate counts
            recent_requests = [t for t in self._request_times if current_time - t < 60]
            hourly_requests = [t for t in self._hourly_request_times if current_time - t < 3600]

            return {
                "requests_last_minute": len(recent_requests),
                "requests_last_hour": len(hourly_requests),
                "daily_tokens_used": self._daily_token_count,
                "concurrent_requests": self._concurrent_requests,
                "daily_tokens_remaining": max(0, LMStudioConfig.MAX_DAILY_TOKENS - self._daily_token_count),
            }


# Global rate limiter instance
instructor_rate_limiter = InstructorRateLimiter()

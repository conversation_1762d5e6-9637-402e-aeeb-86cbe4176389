# LM Studio Configuration
LMSTUDIO_HOST=localhost:1234
LMSTUDIO_MODEL=
LMSTUDIO_MAX_TOKENS=2048
LMSTUDIO_TEMPERATURE=0.1
LMSTUDIO_TIMEOUT=60

# Instructor/LLM Limitation Settings
# Token limits
LMSTUDIO_MIN_TOKENS=50
LMSTUDIO_MAX_TOKENS_PER_REQUEST=4096
LMSTUDIO_MAX_DAILY_TOKENS=100000

# Rate limiting
LMSTUDIO_MAX_REQUESTS_PER_MINUTE=10
LMSTUDIO_MAX_REQUESTS_PER_HOUR=100
LMSTUDIO_MAX_CONCURRENT_REQUESTS=3

# Processing limits
LMSTUDIO_MAX_PROCESSING_TIME=120
LMSTUDIO_MAX_RESPONSE_SIZE=10240
LMSTUDIO_MAX_IMAGE_FILE_SIZE=10485760

# Content filtering
LMSTUDIO_ENABLE_CONTENT_FILTER=true

# Resource limits
LMSTUDIO_MAX_MEMORY_USAGE_MB=512
LMSTUDIO_ENABLE_RESOURCE_MONITORING=true

# Fallback Configuration
ENABLE_TESSERACT_FALLBACK=false

# Application Configuration
DEBUG=true
LOG_LEVEL=INFO

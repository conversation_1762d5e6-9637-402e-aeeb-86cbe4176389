#!/usr/bin/env python3
"""
Test script for the enhanced Cambodian ID card OCR system prompt.
This script demonstrates how to use the improved prompt with LM Studio.
"""

import lmstudio as lms
import json
import time
from PIL import Image
import io
import base64
from typing import Dict, Any

# Enhanced system prompt for Cambodian ID card OCR
CAMBODIAN_ID_CARD_PROMPT = """You are an intelligent document extraction assistant designed specifically for parsing Cambodian ID cards.

ROLE: You are an expert OCR system with specialized knowledge of Cambodian ID card formats, Khmer script, and document structure.

TASK: Extract text from a Cambodian ID card and return it in structured JSON format.

FIELDS TO EXTRACT:
{
  "id_number": "The unique ID number (typically 12 digits, e.g., IDKHM0703698038)",
  "passport_number": "Passport number if available (e.g., 0207108F3009244KHM)",
  "name": "Full name of the cardholder",
  "name_kh": "Name in Khmer script",
  "name_en": "Name in English/Latin script",
  "phone_number": "Phone number if visible",
  "address": "Full address listed on the ID card",
  "birthdate": "Date of birth (maintain original format)",
  "gender": "Gender (Male/Female or ប្រុស/ស្រី)",
  "nationality": "Nationality (usually Cambodian/ខ្មែរ)",
  "height": "Height if visible",
  "birth_place": "Place of birth if visible",
  "issue_date": "Issue date if visible",
  "expiry_date": "Expiry date if visible"
}

KHMER TEXT RECOGNITION PATTERNS:
- ឈ្មោះ (Name) → Look for names in Khmer Unicode range ក-៹
- លេខសម្គាល់ (ID Number) → Usually 12 digits
- ថ្ងៃកំណើត (Date of Birth) → Date patterns DD/MM/YYYY or DD-MM-YYYY
- ភេទ (Gender) → ប្រុស (Male) or ស្រី (Female)
- សញ្ជាតិ (Nationality) → ខ្មែរ (Khmer/Cambodian)
- អាសយដ្ឋាន (Address) → Full address information
- កម្ពស់ (Height) → Height measurements
- ទីកន្លែងកំណើត (Place of Birth) → Birth location

EXTRACTION GUIDELINES:
1. Extract ALL visible text maintaining original formatting
2. If any field is missing/unclear, use "Not Available" or leave blank
3. Infer gender from naming conventions if not explicitly stated
4. Pay special attention to 12-digit ID numbers
5. Maintain original date formats when possible
6. Look for both Khmer and English text versions

RESPONSE FORMAT:
First provide raw extracted text, then structured JSON data:

RAW TEXT:
[All visible text from the image]

STRUCTURED JSON:
{
  "id_number": "extracted_id",
  "name_kh": "khmer_name",
  "name_en": "english_name",
  "birthdate": "birth_date",
  "gender": "gender",
  "nationality": "nationality",
  "address": "full_address",
  "height": "height_if_visible",
  "birth_place": "birth_place_if_visible",
  "issue_date": "issue_date_if_visible",
  "expiry_date": "expiry_date_if_visible"
}

Please analyze the image and extract all information following this format."""


def test_enhanced_prompt_text_only():
    """Test the enhanced prompt with text-only LLM (no image)."""
    print("🧪 Testing Enhanced Cambodian ID Card OCR Prompt (Text-Only)")
    print("=" * 60)
    
    try:
        # Initialize LM Studio model
        model = lms.llm()
        
        # Create a test scenario with sample ID card text
        test_text = """
        Sample Cambodian ID card text for testing:
        
        ឈ្មោះ: សុខ វិចិត្រា
        Name: SOKH VICHETRA
        លេខសម្គាល់: 123456789012
        ID: 123456789012
        ថ្ងៃកំណើត: 15/03/1990
        Date of Birth: 15/03/1990
        ភេទ: ស្រី
        Sex: F
        សញ្ជាតិ: ខ្មែរ
        Nationality: Cambodian
        អាសយដ្ឋាន: ភូមិ១ ឃុំ២ ស្រុក៣ ខេត្តកំពង់ចាម
        Address: Village 1, Commune 2, District 3, Kampong Cham Province
        """
        
        # Combine prompt with test text
        full_prompt = f"{CAMBODIAN_ID_CARD_PROMPT}\n\nTEXT TO ANALYZE:\n{test_text}"
        
        # Create chat and get response
        chat = lms.Chat()
        chat.add_user_message(full_prompt)
        
        print("📤 Sending request to LM Studio...")
        start_time = time.time()
        
        response = model.respond(chat)
        
        processing_time = time.time() - start_time
        
        print(f"✅ Response received in {processing_time:.2f} seconds")
        print("\n📋 LLM Response:")
        print("-" * 40)
        print(str(response))
        print("-" * 40)
        
        return str(response)
        
    except Exception as e:
        print(f"❌ Error testing enhanced prompt: {str(e)}")
        return None


def test_with_image(image_path: str):
    """Test the enhanced prompt with an actual image (requires vision model)."""
    print(f"🖼️  Testing Enhanced Prompt with Image: {image_path}")
    print("=" * 60)
    
    try:
        # Load and prepare image
        image = Image.open(image_path)
        print(f"📸 Image loaded: {image.size} pixels, mode: {image.mode}")
        
        # Convert image to base64 (for vision models)
        buffer = io.BytesIO()
        if image.mode != 'RGB':
            image = image.convert('RGB')
        image.save(buffer, format='JPEG', quality=95)
        img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        # Initialize LM Studio model (requires vision-capable model)
        model = lms.llm()
        
        # Create chat with image and prompt
        chat = lms.Chat()
        chat.add_user_message(CAMBODIAN_ID_CARD_PROMPT)
        # Note: Image handling depends on your specific vision model setup
        
        print("📤 Sending image and prompt to LM Studio...")
        start_time = time.time()
        
        response = model.respond(chat)
        
        processing_time = time.time() - start_time
        
        print(f"✅ Response received in {processing_time:.2f} seconds")
        print("\n📋 LLM Response:")
        print("-" * 40)
        print(str(response))
        print("-" * 40)
        
        return str(response)
        
    except Exception as e:
        print(f"❌ Error testing with image: {str(e)}")
        return None


def parse_response_json(response_text: str) -> Dict[str, Any]:
    """Extract JSON from LLM response."""
    try:
        # Look for JSON in the response
        start_idx = response_text.find('{')
        end_idx = response_text.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = response_text[start_idx:end_idx]
            return json.loads(json_str)
        else:
            print("⚠️  No JSON found in response")
            return {}
            
    except json.JSONDecodeError as e:
        print(f"⚠️  JSON parsing error: {e}")
        return {}


def main():
    """Main test function."""
    print("🚀 Enhanced Cambodian ID Card OCR Prompt Test")
    print("=" * 60)
    
    # Test 1: Text-only prompt
    response = test_enhanced_prompt_text_only()
    
    if response:
        print("\n🔍 Attempting to parse JSON from response...")
        parsed_data = parse_response_json(response)
        
        if parsed_data:
            print("✅ Successfully parsed JSON:")
            print(json.dumps(parsed_data, indent=2, ensure_ascii=False))
        else:
            print("❌ Could not parse JSON from response")
    
    # Test 2: With image (if available)
    image_path = "id_card.jpg"  # Update with your test image path
    try:
        if Image.open(image_path):
            print(f"\n🖼️  Found test image: {image_path}")
            print("Note: Image testing requires a vision-capable model in LM Studio")
            # Uncomment to test with image:
            # test_with_image(image_path)
    except:
        print(f"\n📝 No test image found at {image_path}")
        print("To test with images, place a Cambodian ID card image at 'id_card.jpg'")
    
    print("\n✨ Test completed!")


if __name__ == "__main__":
    main()
